#!/usr/bin/env python3

import os
import sys
import subprocess  # <-- MISSING IMPORT ADDED
import tempfile
from argparse import ArgumentParser, Namespace
import psycopg2
import dotenv


# Database connection details
DB_HOST = "************"
DB_PORT = 5432
DB_ADMIN_USER = "postgres"  # Changed from DB_USER to clarify this is for connection
DB_TEMPLATE = "template1"

# File paths and patterns
BASE_TENANT_PATH = "/mnt/storage/tenants"
SQL_FILE_PATTERN = "{base_path}/{customer_name}/db/db_setup_{customer_name}.sql"
ENV_FILE_PATTERN = "{base_path}/{customer_name}/.env"

# Database and user naming patterns
DB_NEXTCLOUD = "{customer_name}_nextcloud"
DB_KEYCLOAK = "{customer_name}_keycloak"
DB_LLDAP = "{customer_name}_lldap"
DB_USER = "{customer_name}_admin"

# Environment variable names
ENV_VAR_DB_PASSWORD = "DB_ADMIN_PASSWORD"


def parse_arguments() -> Namespace:
    parser = ArgumentParser(description="Setup tenant database.")
    parser.add_argument("customer_name", help="Customer name")
    parser.add_argument("--reset", action="store_true", default=False, help="Reset existing databases and user")
    parser.add_argument("--status", action="store_true", default=False, help="Show status of databases and user")
    return parser.parse_args()


def read_env_file(env_file: str) -> dict:
    dotenv.load_dotenv(env_file)
    db_admin_password = os.getenv(ENV_VAR_DB_PASSWORD)
    if not db_admin_password:
        raise ValueError(f"{ENV_VAR_DB_PASSWORD} not found in .env file")
    return {"db_admin_password": db_admin_password}


def create_pgpasstmp(db_admin_password: str) -> str:
    pgpass_file = tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8')
    pgpass_file.write(f"{DB_HOST}:{DB_PORT}:*:{DB_ADMIN_USER}:{db_admin_password}\n")
    pgpass_file.close()
    return pgpass_file.name


def get_db_count(cursor, customer_name):
    cursor.execute(
        """
        SELECT COUNT(*) FROM pg_database 
        WHERE datname IN (%s, %s, %s);
        """,
        (
            DB_NEXTCLOUD.format(customer_name=customer_name), 
            DB_KEYCLOAK.format(customer_name=customer_name), 
            DB_LLDAP.format(customer_name=customer_name)
        ),
    )
    return cursor.fetchone()[0]


def get_user_count(cursor, customer_name):
    cursor.execute(
        """
        SELECT COUNT(*) FROM pg_roles 
        WHERE rolname = %s;
        """,
        (DB_USER.format(customer_name=customer_name),),
    )
    return cursor.fetchone()[0]


def run_psql_command(command, error_message):
    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {error_message}")
        print(f"Command error: {result.stderr}")
        raise Exception(error_message)
    return result


def check_existing_objects(args, env):
    db_nextcloud = DB_NEXTCLOUD.format(customer_name=args.customer_name)
    db_keycloak = DB_KEYCLOAK.format(customer_name=args.customer_name)
    db_lldap = DB_LLDAP.format(customer_name=args.customer_name)
    db_user = DB_USER.format(customer_name=args.customer_name)
    
    with psycopg2.connect(
        user=DB_ADMIN_USER,  
        host=DB_HOST, 
        port=DB_PORT,
        dbname=DB_TEMPLATE, 
        password=env["db_admin_password"]
    ) as conn:
        with conn.cursor() as cur:
            db_count = get_db_count(cur, args.customer_name)
            user_exists = get_user_count(cur, args.customer_name)
            
            # Status operation
            if args.status:
                print(f"Status for customer {args.customer_name}:")
                print(f"- User {db_user} exists" if user_exists else f"- User {db_user} does not exist")
                if db_count > 0:
                    print("- Databases:")
                    cur.execute(
                        """
                        SELECT datname FROM pg_database 
                        WHERE datname IN (%s, %s, %s);
                        """,
                        (db_nextcloud, db_keycloak, db_lldap),
                    )
                    for row in cur.fetchall():
                        print(f"  - {row[0]}")
                else:
                    print("- No databases found")
                return

            # Reset operation
            if args.reset:
                if db_count == 0 and user_exists == 0:
                    print("No objects to delete.")
                    return

                print("Following objects will be deleted:")
                if user_exists:
                    print(f"- User {db_user}")
                if db_count > 0:
                    print("- Databases:")
                    cur.execute(
                        """
                        SELECT datname FROM pg_database 
                        WHERE datname IN (%s, %s, %s);
                        """,
                        (db_nextcloud, db_keycloak, db_lldap),
                    )
                    for row in cur.fetchall():
                        print(f"  - {row[0]}")

                reset_sql = tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8')
                reset_sql_name = reset_sql.name
                try:
                    with open(reset_sql_name, 'w', encoding='utf-8') as sql_file:
                        sql_file.write(
                            f"""
                            -- Terminate connections
                            SELECT pg_terminate_backend(pid) 
                            FROM pg_stat_activity 
                            WHERE datname IN ('{db_nextcloud}', '{db_keycloak}', '{db_lldap}');

                            -- Drop databases
                            DROP DATABASE IF EXISTS {db_nextcloud};
                            DROP DATABASE IF EXISTS {db_keycloak};
                            DROP DATABASE IF EXISTS {db_lldap};

                            -- Drop user (this must come after dropping the databases)
                            DROP USER IF EXISTS {db_user};
                            """
                        )

                    psql_command = [
                        "psql",
                        "-U", DB_ADMIN_USER,
                        "-h", DB_HOST,
                        "-p", str(DB_PORT),
                        "-d", DB_TEMPLATE,
                        "-f", reset_sql_name,
                        "--no-psqlrc"
                    ]
                    run_psql_command(psql_command, "Failed to reset database")
                    print("Deletion operation completed.")
                finally:
                    os.remove(reset_sql_name)
                return

            # Default operation: Setup
            # If no flag is provided, do setup
            if db_count > 0 or user_exists > 0:
                print(f"Setup cannot be performed because objects already exist for {args.customer_name}:")
                if user_exists:
                    print(f"- User {db_user} already exists")
                if db_count > 0:
                    print("- Databases already exist:")
                    cur.execute(
                        """
                        SELECT datname FROM pg_database 
                        WHERE datname IN (%s, %s, %s);
                        """,
                        (db_nextcloud, db_keycloak, db_lldap),
                    )
                    for row in cur.fetchall():
                        print(f"  - {row[0]}")

                print("Use --reset first to delete existing objects and then run the setup again.")
                return

            sql_file = SQL_FILE_PATTERN.format(
                base_path=BASE_TENANT_PATH,
                customer_name=args.customer_name
            )
            if not os.path.isfile(sql_file):
                raise FileNotFoundError(f"SQL file does not exist: {sql_file}")

            psql_command = [
                "psql",
                "-U", DB_ADMIN_USER,
                "-h", DB_HOST,
                "-p", str(DB_PORT),
                "-d", DB_TEMPLATE,
                "-f", sql_file,
                "--no-psqlrc"
            ]
            run_psql_command(psql_command, "Failed to setup database")
            print("DB Setup completed successfully.")


if __name__ == "__main__":
    args = parse_arguments()
    env_file = ENV_FILE_PATTERN.format(
        base_path=BASE_TENANT_PATH,
        customer_name=args.customer_name
    )
    if not os.path.isfile(env_file):
        raise FileNotFoundError(f"Environment file does not exist: {env_file}")

    env = read_env_file(env_file)
    pgpass_filename = create_pgpasstmp(env["db_admin_password"])
    try:
        # Set environment variable for psql
        os.environ["PGPASSFILE"] = pgpass_filename
        check_existing_objects(args, env)
    finally:
        # Clean up temporary file
        if os.path.exists(pgpass_filename):
            os.remove(pgpass_filename)