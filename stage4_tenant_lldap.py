#!/usr/bin/env python3

import os
import sys
import json
import requests
import urllib3
from dotenv import load_dotenv

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Color codes for output
GREEN = '\033[0;32m'
RED = '\033[0;31m'
YELLOW = '\033[0;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'  # No Color

def print_colored(color, message):
    """Print with color"""
    print(f"{color}{message}{NC}")

def load_portainer_config():
    """Load Portainer configuration from .env file"""
    global_env_path = "/mnt/storage/docker/.env"
    
    if not os.path.isfile(global_env_path):
        print_colored(RED, f"Error: .env file not found at {global_env_path}")
        sys.exit(1)
    
    load_dotenv(global_env_path)
    
    api_key = os.getenv("PORTAINER_API_KEY")
    host = os.getenv("PORTAINER_HOST")
    
    if not api_key or not host:
        print_colored(RED, "Error: PORTAINER_API_KEY or PORTAINER_HOST not defined")
        sys.exit(1)
    
    return {
        "api_key": api_key,
        "host": host,
        "endpoint_id": os.getenv("ENDPOINT_ID", "11"),
        "portainer_url": f"https://{host}:9443"
    }

def execute_command_in_container(customer_name, container_suffix, command, config):
    """Execute a command in a specific container via Portainer API"""
    container_name = f"{customer_name}-{container_suffix}"
    endpoint_id = config["endpoint_id"]
    
    # API headers
    headers = {
        "X-API-Key": config["api_key"],
        "Content-Type": "application/json"
    }
    
    # Get list of containers
    print_colored(BLUE, f"Finding container: {container_name}...")
    
    containers_url = f"{config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/json?all=true"
    
    try:
        response = requests.get(
            containers_url,
            headers=headers,
            verify=False
        )
        
        if response.status_code != 200:
            print_colored(RED, f"Error getting container list: {response.status_code}")
            print_colored(RED, f"Response: {response.text}")
            return False
        
        # Find the specified container
        container_id = None
        for container in response.json():
            for name in container.get("Names", []):
                # Remove leading slash if present
                clean_name = name[1:] if name.startswith("/") else name
                if clean_name == container_name:
                    container_id = container.get("Id")
                    break
            if container_id:
                break
        
        if not container_id:
            print_colored(RED, f"Container {container_name} not found")
            return False
        
        print_colored(BLUE, f"Found container ID: {container_id}")
        
        # Create exec instance
        print_colored(BLUE, f"Creating exec instance for command: {command}")
        
        exec_url = f"{config['portainer_url']}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/exec"
        
        exec_payload = {
            "AttachStdin": False,
            "AttachStdout": True,
            "AttachStderr": True,
            "Tty": False,
            "Cmd": command if isinstance(command, list) else command.split()
        }
        
        response = requests.post(
            exec_url,
            headers=headers,
            json=exec_payload,
            verify=False
        )
        
        if response.status_code != 201:
            print_colored(RED, f"Error creating exec instance: {response.status_code}")
            print_colored(RED, f"Response: {response.text}")
            return False
        
        exec_id = response.json().get("Id")
        print_colored(BLUE, f"Created exec instance with ID: {exec_id}")
        
        # Start the exec instance
        print_colored(BLUE, "Starting exec instance...")
        
        start_url = f"{config['portainer_url']}/api/endpoints/{endpoint_id}/docker/exec/{exec_id}/start"
        start_payload = {
            "Detach": False,
            "Tty": False
        }
        
        response = requests.post(
            start_url,
            headers=headers,
            json=start_payload,
            verify=False
        )
        
        if response.status_code != 200:
            print_colored(RED, f"Error starting exec instance: {response.status_code}")
            print_colored(RED, f"Response: {response.text}")
            return False
        
        # Check the exec instance status
        print_colored(BLUE, "Checking exec instance result...")
        
        inspect_url = f"{config['portainer_url']}/api/endpoints/{endpoint_id}/docker/exec/{exec_id}/json"
        
        response = requests.get(
            inspect_url,
            headers=headers,
            verify=False
        )
        
        if response.status_code != 200:
            print_colored(RED, f"Error inspecting exec instance: {response.status_code}")
            print_colored(RED, f"Response: {response.text}")
            return False
        
        exec_result = response.json()
        exit_code = exec_result.get("ExitCode")
        
        if exit_code == 0:
            print_colored(GREEN, f"Command executed successfully in container {container_name}")
            return True
        else:
            print_colored(RED, f"Command failed with exit code {exit_code}")
            return False
        
    except Exception as e:
        print_colored(RED, f"Exception: {str(e)}")
        return False

def main():
    # Check arguments
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <customer_name>")
        sys.exit(1)
    
    customer_name = sys.argv[1]
    
    # Load Portainer configuration
    config = load_portainer_config()
    
    print_colored(GREEN, f"Executing LLDAP bootstrap for customer: {customer_name}")
    print_colored(YELLOW, f"Using Portainer at: {config['portainer_url']}")
    print_colored(YELLOW, f"With endpoint ID: {config['endpoint_id']}")
    print("----------------------------------------")
    
    # Execute the bootstrap.sh command in the LLDAP container
    result = execute_command_in_container(
        customer_name=customer_name,
        container_suffix="lldap",
        command="./bootstrap.sh",
        config=config
    )
    
    if result:
        print_colored(GREEN, "LLDAP bootstrap completed successfully.")
        sys.exit(0)
    else:
        print_colored(RED, "LLDAP bootstrap failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()