#!/usr/bin/env python3

import os
import sys
import json
import requests
import time
from dotenv import load_dotenv

# Farbcodes für die Ausgabe
GREEN = '\033[0;32m'
RED = '\033[0;31m'
YELLOW = '\033[0;33m'
BLUE = '\033[0;34m'
NC = '\033[0m'  # No Color

def print_colored(color, message):
    """Ausgabe mit Farbe"""
    print(f"{color}{message}{NC}")

def load_config():
    """Lädt die Konfiguration aus der .env-Datei"""
    global_env_path = "/mnt/storage/docker/.env"
    
    if not os.path.isfile(global_env_path):
        print_colored(RED, f"Fehler: .env-Datei nicht gefunden unter {global_env_path}")
        sys.exit(1)
    
    load_dotenv(global_env_path)
    
    api_key = os.getenv("PORTAINER_API_KEY")
    host = os.getenv("PORTAINER_HOST")
    
    if not api_key or not host:
        print_colored(RED, "Fehler: PORTAINER_API_KEY oder PORTAINER_HOST nicht definiert")
        sys.exit(1)
    
    return {
        "api_key": api_key,
        "host": host,
        "endpoint_id": os.getenv("ENDPOINT_ID", "11"),  # Default jetzt 11
        "portainer_url": f"https://{host}:9443"
    }

def parse_env_file(env_file_path):
    """Liest Umgebungsvariablen aus einer .env-Datei"""
    env_vars = []
    
    if not os.path.isfile(env_file_path):
        return env_vars
    
    with open(env_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            if '=' in line:
                key, value = line.split('=', 1)
                env_vars.append({"name": key.strip(), "value": value.strip()})
    
    return env_vars

def deploy_stack(customer_name, config):
    """Deployt einen Stack mit dem angegebenen Endpoint"""
    # Pfade definieren
    tenants_dir = "/mnt/storage/tenants"
    customer_dir = os.path.join(tenants_dir, customer_name)
    stack_name = f"leos360-{customer_name}"
    endpoint_id = config["endpoint_id"]
    
    # Prüfe ob Kundenverzeichnis existiert
    if not os.path.isdir(customer_dir):
        print_colored(RED, f"Fehler: Verzeichnis {customer_dir} existiert nicht")
        sys.exit(1)
    
    # Finde docker-compose Datei
    compose_file = os.path.join(customer_dir, "docker-compose.yaml")
    if not os.path.isfile(compose_file):
        compose_file = os.path.join(customer_dir, "docker-compose.yml")
        if not os.path.isfile(compose_file):
            print_colored(RED, f"Fehler: Keine docker-compose.y(a)ml gefunden in {customer_dir}")
            sys.exit(1)
    
    print_colored(BLUE, f"Verwende Compose-Datei: {compose_file}")
    
    # Lese .env-Datei
    env_file = os.path.join(customer_dir, ".env")
    env_vars = parse_env_file(env_file)
    
    if env_vars:
        print_colored(BLUE, f"Gefundene Umgebungsvariablen: {len(env_vars)}")
    else:
        print_colored(YELLOW, "Keine Umgebungsvariablen gefunden")
    
    # API-Header
    headers = {
        "X-API-Key": config["api_key"],
        "Content-Type": "application/json"
    }
    
    # Prüfe ob Stack existiert
    print_colored(BLUE, f"Prüfe ob Stack {stack_name} bereits existiert...")
    response = requests.get(
        f"{config['portainer_url']}/api/stacks",
        headers=headers,
        verify=False
    )
    
    if response.status_code != 200:
        print_colored(RED, f"Fehler beim Abrufen der Stacks: {response.status_code}")
        print_colored(RED, f"Response: {response.text}")
        sys.exit(1)
    
    stacks = response.json()
    stack_exists = False
    stack_id = None
    
    for stack in stacks:
        if stack.get("Name") == stack_name:
            stack_exists = True
            stack_id = stack.get("Id")
            break
    
    # Lese die Compose-Datei
    with open(compose_file, 'r') as f:
        compose_content = f.read()
    
    if stack_exists:
        print_colored(YELLOW, f"Stack {stack_name} existiert (ID: {stack_id}). Wird aktualisiert...")
        
        # UPDATE des Stacks (anstatt löschen/neu erstellen)
        # Bereite die Daten für das Update vor
        update_payload = {
            "stackFileContent": compose_content,
            "env": env_vars,
            "prune": True  # Entfernt nicht mehr benötigte Container
        }
        
        # API-Aufruf zum Aktualisieren des Stacks
        update_url = f"{config['portainer_url']}/api/stacks/{stack_id}?endpointId={endpoint_id}"
        print_colored(BLUE, f"API-Aufruf: PUT {update_url}")
        
        try:
            response = requests.put(
                update_url,
                headers=headers,
                json=update_payload,
                verify=False
            )
            
            print_colored(BLUE, f"Status Code: {response.status_code}")
            print_colored(BLUE, f"Response: {response.text[:500]}..." if len(response.text) > 500 else response.text)
            
            if response.status_code >= 200 and response.status_code < 300:
                print_colored(GREEN, f"Stack {stack_name} erfolgreich aktualisiert!")
                return True
            else:
                print_colored(RED, f"Fehler beim Aktualisieren des Stacks: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                
                # Wenn Update fehlschlägt, versuchen wir den Stack zu löschen und neu zu erstellen
                print_colored(YELLOW, "Versuche Stack zu löschen und neu zu erstellen...")
                
                # Lösche den Stack
                delete_response = requests.delete(
                    f"{config['portainer_url']}/api/stacks/{stack_id}",
                    headers=headers,
                    verify=False
                )
                
                if delete_response.status_code not in [200, 204, 404]:
                    print_colored(RED, f"Fehler beim Löschen des Stacks: {delete_response.status_code}")
                    print_colored(RED, f"Response: {delete_response.text}")
                    return False
                
                print_colored(GREEN, "Stack erfolgreich gelöscht")
                
                # Warte 3 Sekunden, damit der Löschvorgang abgeschlossen wird
                print_colored(YELLOW, "Warte 3 Sekunden...")
                time.sleep(3)
                
                # Stack neu erstellen
                stack_exists = False
        except Exception as e:
            print_colored(RED, f"Exception bei Stack-Update: {str(e)}")
            return False
    
    # Erstelle neuen Stack, falls er nicht existiert oder das Update fehlgeschlagen ist
    if not stack_exists:
        print_colored(YELLOW, f"Erstelle Stack: {stack_name} mit Endpoint ID: {endpoint_id}")
        
        # Bereite multipart/form-data vor
        files = {'file': (os.path.basename(compose_file), open(compose_file, 'rb'))}
        data = {
            'Name': stack_name,
            'Env': json.dumps(env_vars)
        }
        
        # Führe API-Aufruf durch - EXAKT gemäß Swagger-Dokumentation
        url = f"{config['portainer_url']}/api/stacks/create/standalone/file?endpointId={endpoint_id}"
        
        # Für den Multipart-Request benötigen wir andere Header (ohne Content-Type)
        multipart_headers = {"X-API-Key": config["api_key"]}
        
        print_colored(BLUE, f"API-Aufruf: POST {url}")
        
        # HTTP-Request
        try:
            response = requests.post(
                url,
                headers=multipart_headers,  # Andere Header für multipart
                files=files,
                data=data,
                verify=False
            )
            
            print_colored(BLUE, f"Status Code: {response.status_code}")
            print_colored(BLUE, f"Response: {response.text[:500]}..." if len(response.text) > 500 else response.text)
            
            if response.status_code >= 200 and response.status_code < 300:
                print_colored(GREEN, f"Stack {stack_name} erfolgreich erstellt!")
                return True
            else:
                print_colored(RED, f"Fehler beim Erstellen des Stacks: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                return False
                
        except Exception as e:
            print_colored(RED, f"Exception: {str(e)}")
            return False

if __name__ == "__main__":
    # Prüfe Anzahl der Argumente
    if len(sys.argv) != 2:
        print(f"Verwendung: {sys.argv[0]} customername")
        sys.exit(1)
    
    # Lade Konfiguration
    config = load_config()
    
    # Deaktiviere SSL-Warnungen
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    # Hole Kundennamen
    customer_name = sys.argv[1]
    
    print_colored(GREEN, f"Deploye Stack für Kunde: {customer_name}")
    print_colored(YELLOW, f"Verwende Portainer unter: {config['portainer_url']}")
    print_colored(YELLOW, f"Mit Endpoint ID: {config['endpoint_id']}")
    print("----------------------------------------")
    
    # Deploye Stack
    result = deploy_stack(customer_name, config)
    
    if result:
        print_colored(GREEN, "Stack-Deployment abgeschlossen.")
    else:
        print_colored(RED, "Stack-Deployment fehlgeschlagen.")
        sys.exit(1)