#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage 3: Docker Stack Deployment
===================================================

This script handles the Docker stack deployment for a new tenant in the LEOS360 platform.
It deploys or updates Docker Compose stacks using the Portainer API, managing the complete
containerized infrastructure for the customer.

Author: LEOS360 Development Team
Version: 2.1
Last Updated: 2025-6-2

Prerequisites:
- Stage 1 and Stage 2 must be completed successfully
- Portainer API must be accessible
- Docker Compose file must exist in tenant directory
- Portainer API credentials must be configured

Usage:
    python3 stage3_tenant_docker.py <customer_name> [options]

Examples:
    python3 stage3_tenant_docker.py example-customer
    python3 stage3_tenant_docker.py example-customer --delete
    python3 stage3_tenant_docker.py example-customer --status
"""

import os
import sys
import json
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any
import requests
import urllib3
from dotenv import load_dotenv

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

# Version information
SCRIPT_VERSION = "2.1"
SCRIPT_NAME = "Stage 3: Docker Stack Deployment"

# File paths
DOCKER_CONFIG_PATH = Path("/mnt/storage/docker/.env")
BASE_TENANT_PATH = Path("/mnt/storage/tenants")

# Portainer configuration
DEFAULT_ENDPOINT_ID = "11"
PORTAINER_PORT = 9443
API_TIMEOUT = 30

# Disable SSL warnings for Portainer API
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_step(step: str) -> None:
    """Print a formatted step message."""
    print(f"[INFO] {step}")


def print_warning(message: str) -> None:
    """Print a formatted warning message."""
    print(f"[WARNING] {message}")


def print_error(message: str) -> None:
    """Print a formatted error message."""
    print(f"[ERROR] {message}")


def print_success(message: str) -> None:
    """Print a formatted success message."""
    print(f"[SUCCESS] {message}")


def print_debug(message: str) -> None:
    """Print a formatted debug message."""
    print(f"[DEBUG] {message}")


# =============================================================================
# MAIN DOCKER STACK DEPLOYMENT CLASS
# =============================================================================

class TenantDockerStackSetup:
    """
    Handles Docker stack deployment for a new tenant in the LEOS360 platform.

    This class manages the complete Docker stack deployment process including:
    - Portainer API configuration and authentication
    - Docker Compose file validation and parsing
    - Environment variable management
    - Stack creation, updating, and deletion
    - Error handling and status reporting
    """

    def __init__(self, customer_name: str) -> None:
        """
        Initialize tenant Docker stack setup.

        Args:
            customer_name: Name of the customer/tenant

        Raises:
            ValueError: If customer_name is invalid
            FileNotFoundError: If required files are missing
        """
        self.customer_name = customer_name
        print_step(f"Initializing Docker stack setup for: {customer_name}")

        # Stack configuration
        self.stack_name = f"leos360-{customer_name}"

        # File paths
        self.customer_dir = BASE_TENANT_PATH / customer_name
        self.env_file = self.customer_dir / ".env"

        # Find docker-compose file
        self.compose_file = self._find_compose_file()

        # Portainer configuration (will be loaded later)
        self.portainer_config = {}

    # =========================================================================
    # UTILITY METHODS
    # =========================================================================

    def _find_compose_file(self) -> Path:
        """
        Find the Docker Compose file in the customer directory.

        Returns:
            Path to the Docker Compose file

        Raises:
            FileNotFoundError: If no compose file is found
        """
        # Check for docker-compose.yaml first, then .yml
        compose_yaml = self.customer_dir / "docker-compose.yaml"
        compose_yml = self.customer_dir / "docker-compose.yml"

        if compose_yaml.exists():
            return compose_yaml
        elif compose_yml.exists():
            return compose_yml
        else:
            raise FileNotFoundError(f"No docker-compose.y(a)ml found in {self.customer_dir}")

    def load_portainer_config(self) -> None:
        """
        Load Portainer configuration from environment file.

        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If required configuration is missing
        """
        if not DOCKER_CONFIG_PATH.exists():
            raise FileNotFoundError(f"Docker config file not found: {DOCKER_CONFIG_PATH}")

        load_dotenv(DOCKER_CONFIG_PATH)

        api_key = os.getenv("PORTAINER_API_KEY")
        host = os.getenv("PORTAINER_HOST")

        if not api_key or not host:
            raise ValueError("PORTAINER_API_KEY or PORTAINER_HOST not defined in config")

        self.portainer_config = {
            "api_key": api_key,
            "host": host,
            "endpoint_id": os.getenv("ENDPOINT_ID", DEFAULT_ENDPOINT_ID),
            "portainer_url": f"https://{host}:{PORTAINER_PORT}"
        }

        print_step("Portainer configuration loaded successfully")

    def parse_env_file(self) -> List[Dict[str, str]]:
        """
        Parse environment variables from customer .env file.

        Returns:
            List of environment variable dictionaries
        """
        env_vars = []

        if not self.env_file.exists():
            print_warning("No .env file found for customer")
            return env_vars

        try:
            with open(self.env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    if '=' in line:
                        key, value = line.split('=', 1)
                        env_vars.append({"name": key.strip(), "value": value.strip()})

            print_step(f"Found {len(env_vars)} environment variables")
            return env_vars

        except Exception as e:
            print_error(f"Error reading environment file: {e}")
            return []

    # =========================================================================
    # PORTAINER API METHODS
    # =========================================================================

    def make_api_request(self, method: str, url: str, data: Optional[Dict] = None,
                        files: Optional[Dict] = None, use_json: bool = True) -> requests.Response:
        """
        Make an API request to Portainer.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: API endpoint URL
            data: Optional request data
            files: Optional files for multipart upload
            use_json: Whether to use JSON headers

        Returns:
            Response object

        Raises:
            Exception: If API request fails
        """
        if use_json:
            headers = {
                "X-API-Key": self.portainer_config["api_key"],
                "Content-Type": "application/json"
            }
        else:
            headers = {"X-API-Key": self.portainer_config["api_key"]}

        try:
            print_debug(f"Making {method} request to {url}")

            if method.upper() == "GET":
                response = requests.get(url, headers=headers, verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, headers=headers, files=files, data=data,
                                           verify=False, timeout=API_TIMEOUT)
                else:
                    response = requests.post(url, headers=headers, json=data,
                                           verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers, json=data,
                                      verify=False, timeout=API_TIMEOUT)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers, verify=False, timeout=API_TIMEOUT)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            print_debug(f"Response status: {response.status_code}")
            return response

        except requests.exceptions.RequestException as e:
            print_error(f"API request failed: {e}")
            raise

    def get_existing_stacks(self) -> List[Dict[str, Any]]:
        """
        Get list of existing stacks from Portainer.

        Returns:
            List of stack dictionaries

        Raises:
            Exception: If API request fails
        """
        url = f"{self.portainer_config['portainer_url']}/api/stacks"
        response = self.make_api_request("GET", url)

        if response.status_code != 200:
            raise Exception(f"Failed to get stacks: {response.status_code} - {response.text}")

        return response.json()

    def find_stack_by_name(self, stack_name: str) -> Optional[Dict[str, Any]]:
        """
        Find a stack by name.

        Args:
            stack_name: Name of the stack to find

        Returns:
            Stack dictionary if found, None otherwise
        """
        stacks = self.get_existing_stacks()

        for stack in stacks:
            if stack.get("Name") == stack_name:
                return stack

        return None

    # =========================================================================
    # STACK OPERATION METHODS
    # =========================================================================

    def create_stack(self, env_vars: List[Dict[str, str]]) -> bool:
        """
        Create a new Docker stack.

        Args:
            env_vars: List of environment variables

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Creating new stack: {self.stack_name}")

        # Prepare multipart form data
        with open(self.compose_file, 'rb') as f:
            files = {'file': (self.compose_file.name, f)}
            data = {
                'Name': self.stack_name,
                'Env': json.dumps(env_vars)
            }

            url = f"{self.portainer_config['portainer_url']}/api/stacks/create/standalone/file?endpointId={self.portainer_config['endpoint_id']}"

            try:
                response = self.make_api_request("POST", url, data=data, files=files, use_json=False)

                if 200 <= response.status_code < 300:
                    print_success(f"Stack {self.stack_name} created successfully!")
                    return True
                else:
                    print_error(f"Failed to create stack: {response.status_code} - {response.text}")
                    return False

            except Exception as e:
                print_error(f"Exception during stack creation: {e}")
                return False

    def update_stack(self, stack_id: str, env_vars: List[Dict[str, str]]) -> bool:
        """
        Update an existing Docker stack.

        Args:
            stack_id: ID of the stack to update
            env_vars: List of environment variables

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Updating existing stack: {self.stack_name} (ID: {stack_id})")

        # Read compose file content
        with open(self.compose_file, 'r') as f:
            compose_content = f.read()

        # Prepare update payload
        update_payload = {
            "stackFileContent": compose_content,
            "env": env_vars,
            "prune": True  # Remove unused containers
        }

        url = f"{self.portainer_config['portainer_url']}/api/stacks/{stack_id}?endpointId={self.portainer_config['endpoint_id']}"

        try:
            response = self.make_api_request("PUT", url, data=update_payload)

            if 200 <= response.status_code < 300:
                print_success(f"Stack {self.stack_name} updated successfully!")
                return True
            else:
                print_error(f"Failed to update stack: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print_error(f"Exception during stack update: {e}")
            return False

    def delete_stack(self, stack_id: str) -> bool:
        """
        Delete a Docker stack.

        Args:
            stack_id: ID of the stack to delete

        Returns:
            True if successful, False otherwise
        """
        print_step(f"Deleting stack: {self.stack_name} (ID: {stack_id})")

        url = f"{self.portainer_config['portainer_url']}/api/stacks/{stack_id}"

        try:
            response = self.make_api_request("DELETE", url)

            if response.status_code in [200, 204, 404]:
                print_success(f"Stack {self.stack_name} deleted successfully!")
                return True
            else:
                print_error(f"Failed to delete stack: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print_error(f"Exception during stack deletion: {e}")
            return False

    def show_stack_status(self) -> None:
        """
        Show status of the Docker stack for this customer.
        """
        print_step(f"Checking stack status for customer: {self.customer_name}")

        try:
            existing_stack = self.find_stack_by_name(self.stack_name)

            if existing_stack:
                print_success(f"Stack {self.stack_name} exists:")
                print(f"  - ID: {existing_stack.get('Id')}")
                print(f"  - Status: {existing_stack.get('Status', 'Unknown')}")
                print(f"  - Endpoint ID: {existing_stack.get('EndpointId')}")
                print(f"  - Created: {existing_stack.get('CreationDate', 'Unknown')}")
            else:
                print_warning(f"Stack {self.stack_name} does not exist")

        except Exception as e:
            print_error(f"Failed to check stack status: {e}")

    # =========================================================================
    # MAIN EXECUTION METHOD
    # =========================================================================

    def run(self, operation: str) -> bool:
        """
        Main execution function that orchestrates the Docker stack deployment process.

        Args:
            operation: Operation to perform ('deploy', 'delete', or 'status')

        Returns:
            True if operation completed successfully, False otherwise
        """
        print_header(f"LEOS360 Docker Stack Deployment - {self.customer_name}")
        print_step(f"Starting Docker stack {operation} for {self.customer_name}...")

        try:
            # Step 1: Validate customer directory
            if not self.customer_dir.exists():
                raise FileNotFoundError(f"Customer directory does not exist: {self.customer_dir}")

            # Step 2: Load Portainer configuration
            self.load_portainer_config()

            # Step 3: Parse environment variables
            env_vars = self.parse_env_file()

            # Step 4: Perform requested operation
            if operation == "status":
                self.show_stack_status()
                return True
            elif operation == "delete":
                existing_stack = self.find_stack_by_name(self.stack_name)
                if existing_stack:
                    success = self.delete_stack(existing_stack["Id"])
                    if success:
                        print_success(f"Stack {self.stack_name} deleted successfully!")
                    return success
                else:
                    print_warning(f"Stack {self.stack_name} does not exist, nothing to delete")
                    return True
            else:  # default: deploy
                existing_stack = self.find_stack_by_name(self.stack_name)

                if existing_stack:
                    # Update existing stack
                    success = self.update_stack(existing_stack["Id"], env_vars)
                    if not success:
                        # If update fails, try delete and recreate
                        print_warning("Update failed, attempting delete and recreate...")
                        if self.delete_stack(existing_stack["Id"]):
                            time.sleep(3)  # Wait for deletion to complete
                            success = self.create_stack(env_vars)
                else:
                    # Create new stack
                    success = self.create_stack(env_vars)

                if success:
                    print_header("OPERATION COMPLETED SUCCESSFULLY")
                    print_success(f"Docker stack deployment for {self.customer_name} completed successfully!")

                return success

        except Exception as e:
            print_error(f"Docker stack {operation} failed: {str(e)}")
            return False


# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """
    Main function to parse arguments and execute Docker stack deployment.
    """
    print_header(f"{SCRIPT_NAME} v{SCRIPT_VERSION}")

    parser = argparse.ArgumentParser(
        description='Deploy Docker stack for LEOS360 platform tenant',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 stage3_tenant_docker.py example-customer
  python3 stage3_tenant_docker.py example-customer --delete
  python3 stage3_tenant_docker.py example-customer --status

Requirements:
  - Stage 1 and Stage 2 must be completed successfully
  - Portainer API must be accessible
  - Docker Compose file must exist in tenant directory
        """
    )
    parser.add_argument(
        'customer_name',
        help='Name of the customer (must match Stage 1 setup)'
    )
    parser.add_argument(
        '--delete',
        action='store_true',
        help='Delete the Docker stack for the customer'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show status of the Docker stack'
    )

    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    args = parser.parse_args()

    # Determine operation
    if args.status:
        operation = "status"
    elif args.delete:
        operation = "delete"
    else:
        operation = "deploy"

    try:
        # Create and run Docker stack setup
        setup = TenantDockerStackSetup(args.customer_name)
        success = setup.run(operation)

        if not success:
            sys.exit(1)

    except ValueError as e:
        print_error(f"Invalid input: {e}")
        sys.exit(1)
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_error("Docker stack deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        sys.exit(1)


def deploy_stack(customer_name, config):
    """Deployt einen Stack mit dem angegebenen Endpoint"""
    # Pfade definieren
    tenants_dir = "/mnt/storage/tenants"
    customer_dir = os.path.join(tenants_dir, customer_name)
    stack_name = f"leos360-{customer_name}"
    endpoint_id = config["endpoint_id"]
    
    # Prüfe ob Kundenverzeichnis existiert
    if not os.path.isdir(customer_dir):
        print_colored(RED, f"Fehler: Verzeichnis {customer_dir} existiert nicht")
        sys.exit(1)
    
    # Finde docker-compose Datei
    compose_file = os.path.join(customer_dir, "docker-compose.yaml")
    if not os.path.isfile(compose_file):
        compose_file = os.path.join(customer_dir, "docker-compose.yml")
        if not os.path.isfile(compose_file):
            print_colored(RED, f"Fehler: Keine docker-compose.y(a)ml gefunden in {customer_dir}")
            sys.exit(1)
    
    print_colored(BLUE, f"Verwende Compose-Datei: {compose_file}")
    
    # Lese .env-Datei
    env_file = os.path.join(customer_dir, ".env")
    env_vars = parse_env_file(env_file)
    
    if env_vars:
        print_colored(BLUE, f"Gefundene Umgebungsvariablen: {len(env_vars)}")
    else:
        print_colored(YELLOW, "Keine Umgebungsvariablen gefunden")
    
    # API-Header
    headers = {
        "X-API-Key": config["api_key"],
        "Content-Type": "application/json"
    }
    
    # Prüfe ob Stack existiert
    print_colored(BLUE, f"Prüfe ob Stack {stack_name} bereits existiert...")
    response = requests.get(
        f"{config['portainer_url']}/api/stacks",
        headers=headers,
        verify=False
    )
    
    if response.status_code != 200:
        print_colored(RED, f"Fehler beim Abrufen der Stacks: {response.status_code}")
        print_colored(RED, f"Response: {response.text}")
        sys.exit(1)
    
    stacks = response.json()
    stack_exists = False
    stack_id = None
    
    for stack in stacks:
        if stack.get("Name") == stack_name:
            stack_exists = True
            stack_id = stack.get("Id")
            break
    
    # Lese die Compose-Datei
    with open(compose_file, 'r') as f:
        compose_content = f.read()
    
    if stack_exists:
        print_colored(YELLOW, f"Stack {stack_name} existiert (ID: {stack_id}). Wird aktualisiert...")
        
        # UPDATE des Stacks (anstatt löschen/neu erstellen)
        # Bereite die Daten für das Update vor
        update_payload = {
            "stackFileContent": compose_content,
            "env": env_vars,
            "prune": True  # Entfernt nicht mehr benötigte Container
        }
        
        # API-Aufruf zum Aktualisieren des Stacks
        update_url = f"{config['portainer_url']}/api/stacks/{stack_id}?endpointId={endpoint_id}"
        print_colored(BLUE, f"API-Aufruf: PUT {update_url}")
        
        try:
            response = requests.put(
                update_url,
                headers=headers,
                json=update_payload,
                verify=False
            )
            
            print_colored(BLUE, f"Status Code: {response.status_code}")
            print_colored(BLUE, f"Response: {response.text[:500]}..." if len(response.text) > 500 else response.text)
            
            if response.status_code >= 200 and response.status_code < 300:
                print_colored(GREEN, f"Stack {stack_name} erfolgreich aktualisiert!")
                return True
            else:
                print_colored(RED, f"Fehler beim Aktualisieren des Stacks: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                
                # Wenn Update fehlschlägt, versuchen wir den Stack zu löschen und neu zu erstellen
                print_colored(YELLOW, "Versuche Stack zu löschen und neu zu erstellen...")
                
                # Lösche den Stack
                delete_response = requests.delete(
                    f"{config['portainer_url']}/api/stacks/{stack_id}",
                    headers=headers,
                    verify=False
                )
                
                if delete_response.status_code not in [200, 204, 404]:
                    print_colored(RED, f"Fehler beim Löschen des Stacks: {delete_response.status_code}")
                    print_colored(RED, f"Response: {delete_response.text}")
                    return False
                
                print_colored(GREEN, "Stack erfolgreich gelöscht")
                
                # Warte 3 Sekunden, damit der Löschvorgang abgeschlossen wird
                print_colored(YELLOW, "Warte 3 Sekunden...")
                time.sleep(3)
                
                # Stack neu erstellen
                stack_exists = False
        except Exception as e:
            print_colored(RED, f"Exception bei Stack-Update: {str(e)}")
            return False
    
    # Erstelle neuen Stack, falls er nicht existiert oder das Update fehlgeschlagen ist
    if not stack_exists:
        print_colored(YELLOW, f"Erstelle Stack: {stack_name} mit Endpoint ID: {endpoint_id}")
        
        # Bereite multipart/form-data vor
        files = {'file': (os.path.basename(compose_file), open(compose_file, 'rb'))}
        data = {
            'Name': stack_name,
            'Env': json.dumps(env_vars)
        }
        
        # Führe API-Aufruf durch - EXAKT gemäß Swagger-Dokumentation
        url = f"{config['portainer_url']}/api/stacks/create/standalone/file?endpointId={endpoint_id}"
        
        # Für den Multipart-Request benötigen wir andere Header (ohne Content-Type)
        multipart_headers = {"X-API-Key": config["api_key"]}
        
        print_colored(BLUE, f"API-Aufruf: POST {url}")
        
        # HTTP-Request
        try:
            response = requests.post(
                url,
                headers=multipart_headers,  # Andere Header für multipart
                files=files,
                data=data,
                verify=False
            )
            
            print_colored(BLUE, f"Status Code: {response.status_code}")
            print_colored(BLUE, f"Response: {response.text[:500]}..." if len(response.text) > 500 else response.text)
            
            if response.status_code >= 200 and response.status_code < 300:
                print_colored(GREEN, f"Stack {stack_name} erfolgreich erstellt!")
                return True
            else:
                print_colored(RED, f"Fehler beim Erstellen des Stacks: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                return False
                
        except Exception as e:
            print_colored(RED, f"Exception: {str(e)}")
            return False

if __name__ == "__main__":
    # Prüfe Anzahl der Argumente
    if len(sys.argv) != 2:
        print(f"Verwendung: {sys.argv[0]} customername")
        sys.exit(1)
    
    # Lade Konfiguration
    config = load_config()
    
    # Deaktiviere SSL-Warnungen
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    # Hole Kundennamen
    customer_name = sys.argv[1]
    
    print_colored(GREEN, f"Deploye Stack für Kunde: {customer_name}")
    print_colored(YELLOW, f"Verwende Portainer unter: {config['portainer_url']}")
    print_colored(YELLOW, f"Mit Endpoint ID: {config['endpoint_id']}")
    print("----------------------------------------")
    
    # Deploye Stack
    result = deploy_stack(customer_name, config)
    
    if result:
        print_colored(GREEN, "Stack-Deployment abgeschlossen.")
    else:
        print_colored(RED, "Stack-Deployment fehlgeschlagen.")
        sys.exit(1)