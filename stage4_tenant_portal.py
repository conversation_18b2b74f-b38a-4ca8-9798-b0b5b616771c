import os
import sys
import requests
import urllib3
import time
from dotenv import load_dotenv

# Fixed configuration for central Keycloak
SSO_URL = "https://sso.leos360.com"
REALM_NAME = "leos360"
KEYCLOAK_ADMIN_USERNAME = None
KEYCLOAK_ADMIN_PASSWORD = None
VERIFY_SSL = True

def load_env_credentials(customer_name):
    """Load Keycloak admin credentials from both customer and sso portal env files"""
    global KEYCLOAK_ADMIN_USERNAME, KEYCLOAK_ADMIN_PASSWORD
    
    # Load customer env file
    customer_env_path = f"/mnt/storage/tenants/{customer_name}/.env"
    if not os.path.exists(customer_env_path):
        print(f"Error: Customer .env file not found at {customer_env_path}")
        sys.exit(1)
    load_dotenv(customer_env_path)
    
    # Load sso portal env file
    sso_env_path = "/mnt/storage/setup/keycloak/.env"
    if not os.path.exists(sso_env_path):
        print(f"Error: SSO portal .env file not found at {sso_env_path}")
        sys.exit(1)
    load_dotenv(sso_env_path, override=True)
    
    KEYCLOAK_ADMIN_USERNAME = os.getenv("KEYCLOAK_ADMIN_USERNAME")
    KEYCLOAK_ADMIN_PASSWORD = os.getenv("KEYCLOAK_ADMIN_PASSWORD")
    
    if not KEYCLOAK_ADMIN_USERNAME or not KEYCLOAK_ADMIN_PASSWORD:
        print("Error: KEYCLOAK_ADMIN_USERNAME and KEYCLOAK_ADMIN_PASSWORD must be set in environment files")
        sys.exit(1)

def get_keycloak_token():
    """Get admin access token from Keycloak"""
    url = f"{SSO_URL}/realms/master/protocol/openid-connect/token"
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    data = {
        "client_id": "admin-cli",
        "username": KEYCLOAK_ADMIN_USERNAME,
        "password": KEYCLOAK_ADMIN_PASSWORD,
        "grant_type": "password"
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, verify=VERIFY_SSL)
        response.raise_for_status()
        token = response.json().get("access_token")
        return token
    except requests.exceptions.RequestException as e:
        print(f"ERROR: Failed to get access token: {e}")
        sys.exit(1)

def check_organization_exists(token, org_name):
    """Check if organization already exists in Keycloak"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations/{org_name}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def create_organization(token, org_name, domain):
    """Create a new organization in Keycloak with domain"""
    if check_organization_exists(token, org_name):
        return org_name
    
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    org_data = {
        "name": org_name,
        "enabled": True,
        "domains": [{"name": domain, "verified": True}]
    }
    
    try:
        response = requests.post(url, headers=headers, json=org_data, verify=VERIFY_SSL)
        response.raise_for_status()
        time.sleep(1)  # Wait for organization to be available
        return org_name
    except requests.exceptions.RequestException as e:
        print(f"Error creating organization: {e}")
        return None

def check_identity_provider_exists(token, idp_alias):
    """Check if identity provider already exists in Keycloak"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances/{idp_alias}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def create_identity_provider(token, idp_alias, kc_hostname, keycloak_realm, customer_domain, client_secret):
    """Create OIDC identity provider with complete configuration"""
    if check_identity_provider_exists(token, idp_alias):
        return idp_alias
    
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    idp_config = {
        "alias": idp_alias,
        "displayName": customer_domain,
        "providerId": "keycloak-oidc",
        "enabled": True,
        "updateProfileFirstLoginMode": "on",
        "trustEmail": False,
        "storeToken": False,
        "addReadTokenRoleOnCreate": False,
        "authenticateByDefault": False,
        "linkOnly": False,
        "hideOnLogin": True,
        "config": {
            "tokenUrl": f"{kc_hostname}/realms/{keycloak_realm}/protocol/openid-connect/token",
            "acceptsPromptNoneForwardFromClient": "false",
            "jwksUrl": f"{kc_hostname}/realms/{keycloak_realm}/protocol/openid-connect/certs",
            "isAccessTokenJWT": "true",
            "filteredByClaim": "false",
            "backchannelSupported": "false",
            "caseSensitiveOriginalUsername": "false",
            "issuer": f"{kc_hostname}/realms/{keycloak_realm}",
            "pkceMethod": "S256",
            "loginHint": "true",
            "clientAuthMethod": "client_secret_post",
            "syncMode": "LEGACY",
            "clientSecret": client_secret,
            "allowedClockSkew": "0",
            "userInfoUrl": f"{kc_hostname}/realms/{keycloak_realm}/protocol/openid-connect/userinfo",
            "validateSignature": "true",
            "clientId": "leos360portal",
            "uiLocales": "false",
            "disableNonce": "false",
            "useJwksUrl": "true",
            "kc.org.domain": customer_domain,
            "kc.org.broker.redirect.mode.email-matches": "true",
            "sendClientIdOnLogout": "false",
            "pkceEnabled": "true",
            "metadataDescriptorUrl": f"{kc_hostname}/realms/{keycloak_realm}/.well-known/openid-configuration",
            "authorizationUrl": f"{kc_hostname}/realms/{keycloak_realm}/protocol/openid-connect/auth",
            "disableUserInfo": "false",
            "logoutUrl": f"{kc_hostname}/realms/{keycloak_realm}/protocol/openid-connect/logout",
            "sendIdTokenOnLogout": "true",
            "passMaxAge": "false"
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=idp_config, verify=VERIFY_SSL)
        response.raise_for_status()
        return idp_alias
    except requests.exceptions.RequestException as e:
        print(f"Error creating identity provider: {e}")
        return None

def export_identity_provider(token, idp_alias):
    """Export existing identity provider configuration"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances/{idp_alias}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        
        idp_config = response.json()
        print(f"\n=== Identity Provider '{idp_alias}' Configuration ===")
        print(json.dumps(idp_config, indent=2))
        return idp_config
    except requests.exceptions.RequestException as e:
        print(f"Error exporting identity provider: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def list_identity_providers(token):
    """List all identity providers in the realm"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/identity-provider/instances"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        
        idp_list = response.json()
        print(f"\n=== Identity Providers in realm '{REALM_NAME}' ===")
        for idp in idp_list:
            print(f"- Alias: {idp.get('alias')}, Provider: {idp.get('providerId')}, Enabled: {idp.get('enabled')}")
        return idp_list
    except requests.exceptions.RequestException as e:
        print(f"Error listing identity providers: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response Status Code: {e.response.status_code}")
            print(f"Response Text: {e.response.text}")
        return None

def get_organization_id(token, org_name):
    """Get the internal ID of an organization by name"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=VERIFY_SSL)
        response.raise_for_status()
        
        organizations = response.json()
        for org in organizations:
            if org.get('name') == org_name:
                return org.get('id')
        return None
    except requests.exceptions.RequestException:
        return None

def link_idp_to_organization(token, org_id, idp_alias):
    """Link an identity provider to an organization using organization ID"""
    url = f"{SSO_URL}/admin/realms/{REALM_NAME}/organizations/{org_id}/identity-providers"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=idp_alias, verify=VERIFY_SSL)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException:
        return False

def main():
    """Main execution function"""
    if len(sys.argv) < 2:
        print("Usage: python setup-tenant-sso.py <customer_name> [export_idp_alias]")
        print("Example: python setup-tenant-sso.py yealinkdev")
        print("Example: python setup-tenant-sso.py yealinkdev export yealinkdev")
        sys.exit(1)
    
    customer_name = sys.argv[1]
    
    # Check for export mode
    if len(sys.argv) > 2 and sys.argv[2] == "export":
        # Load credentials
        load_env_credentials(customer_name)
        token = get_keycloak_token()
        
        if len(sys.argv) > 3:
            # Export specific IdP
            idp_alias = sys.argv[3]
            export_identity_provider(token, idp_alias)
        else:
            # List all IdPs
            list_identity_providers(token)
        return
    
    org_name = customer_name
    
    # Load credentials and get values from .env
    load_env_credentials(customer_name)
    
    domain = os.getenv("CUSTOMER_DOMAIN")
    kc_hostname = os.getenv("KC_HOSTNAME")
    keycloak_realm = os.getenv("KEYCLOAK_REALM")
    client_secret = os.getenv("LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET")
    
    if not domain:
        print("Error: CUSTOMER_DOMAIN must be set in customer .env file")
        sys.exit(1)
    
    if not kc_hostname:
        print("Error: KC_HOSTNAME must be set in customer .env file")
        sys.exit(1)
        
    if not keycloak_realm:
        print("Error: KEYCLOAK_REALM must be set in customer .env file")
        sys.exit(1)
        
    if not client_secret:
        print("Error: LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET must be set in customer .env file")
        sys.exit(1)
    
    # Get admin token
    token = get_keycloak_token()
    
    # Create organization
    result = create_organization(token, org_name, domain)
    
    if result:
        print("✅ Organization created successfully!")
        
        # Create identity provider
        idp_result = create_identity_provider(token, org_name, kc_hostname, keycloak_realm, domain, client_secret)
        
        if idp_result:
            print("✅ Identity Provider created successfully!")
            
            # Link IdP to organization
            org_id = get_organization_id(token, org_name)
            if org_id:
                link_result = link_idp_to_organization(token, org_id, org_name)
                if link_result:
                    print("✅ Identity Provider linked to organization successfully!")
                else:
                    print("❌ Failed to link Identity Provider to organization.")
                    sys.exit(1)
            else:
                print("❌ Failed to get organization ID for linking.")
                sys.exit(1)
        else:
            print("❌ Failed to create Identity Provider.")
            sys.exit(1)
    else:
        print("❌ Failed to create organization.")
        sys.exit(1)

if __name__ == "__main__":
    main()