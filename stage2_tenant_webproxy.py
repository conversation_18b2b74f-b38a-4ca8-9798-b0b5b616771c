#!/usr/bin/env python3

import sys
import argparse
import requests
import logging
from os import path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_customer_ip(customer_name):
    """Get customer IP from .env file"""
    env_path = f"/mnt/storage/tenants/{customer_name}/.env"
    try:
        with open(env_path, 'r') as f:
            for line in f:
                if line.startswith('CUSTOMER_IP='):
                    return line.strip().split('=', 1)[1]
        logging.error(f"Error: CUSTOMER_IP not found in {env_path}")
        raise ValueError(f"CUSTOMER_IP not found in {env_path}")
    except Exception as e:
        logging.error(f"Error: Could not read CUSTOMER_IP from {env_path}, Detail: {str(e)}")
        raise

def api_call(url, method='POST', payload=None):
    """Make an API call with optional payload"""
    headers = {}
    
    try:
        if payload:
            response = requests.request(method, url, json=payload, headers=headers, timeout=10)
        else:
            response = requests.request(method, url, headers=headers, timeout=10)

        if response.status_code != 200:
            logging.error(f"Error: Failed API call to {url}, API response: {response.text}")
            raise Exception(f"Failed API call to {url}")

        logging.info(f"API call successful to {url}")
        return response.json()

    except requests.exceptions.RequestException as e:
        logging.error(f"Error: Failed to connect to API, Detail: {str(e)}")
        raise

def setup_webproxy(customer_name, customer_ip):
    """Setup web proxy via API call"""
    api_url = "http://************:5000/webproxy/add/" + customer_name
    payload = {"ip": customer_ip}
    return api_call(api_url, method='POST', payload=payload)

def delete_webproxy(customer_name):
    """Delete web proxy via API call"""
    api_url = "http://************:5000/webproxy/del/" + customer_name
    return api_call(api_url, method='DELETE')

def main():
    parser = argparse.ArgumentParser(description='Setup or delete web proxy for a tenant')
    parser.add_argument('customer_name', help='Name of the customer')
    parser.add_argument('--ip', help='IP address of the customer (optional, will be read from .env if not provided)')
    parser.add_argument('-d', '--delete', action='store_true', help='Delete the web proxy for the customer')

    args = parser.parse_args()

    try:
        if args.delete:
            result = delete_webproxy(args.customer_name)
        else:
            # Use provided IP if available, otherwise read from .env
            customer_ip = args.ip if args.ip else get_customer_ip(args.customer_name)
            result = setup_webproxy(args.customer_name, customer_ip)
        sys.exit(0)
    except Exception as e:
        logging.error(f"Error setting up or deleting web proxy: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
